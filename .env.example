# Database
DATABASE_URL="postgresql://username:password@localhost:5432/logomind"
DIRECT_URL="postgresql://username:password@localhost:5432/logomind"

# NextAuth.js
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="http://localhost:3000"

# AI API Keys
HUGGINGFACE_API_KEY="your-huggingface-api-key"
REPLICATE_API_TOKEN="your-replicate-api-token"
STABILITY_API_KEY="your-stability-ai-api-key"
OPENAI_API_KEY="your-openai-api-key"

# Supabase (Alternative to PostgreSQL)
NEXT_PUBLIC_SUPABASE_URL="your-supabase-project-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# AWS S3 (File Storage)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_S3_BUCKET="your-s3-bucket-name"
AWS_REGION="us-east-1"

# Cloudinary (Alternative File Storage)
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Redis (Caching)
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD="your-redis-password"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Stripe (Payment Processing)
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"

# Email Service (Resend)
RESEND_API_KEY="your-resend-api-key"
FROM_EMAIL="<EMAIL>"

# Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS="G-XXXXXXXXXX"
NEXT_PUBLIC_POSTHOG_KEY="your-posthog-key"
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"

# Rate Limiting
UPSTASH_REDIS_REST_URL="your-upstash-redis-url"
UPSTASH_REDIS_REST_TOKEN="your-upstash-redis-token"

# Feature Flags
NEXT_PUBLIC_ENABLE_BETA_FEATURES="false"
NEXT_PUBLIC_ENABLE_ANALYTICS="true"
NEXT_PUBLIC_ENABLE_ERROR_REPORTING="true"

# App Configuration
NEXT_PUBLIC_APP_NAME="LogoMind"
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# Development
NODE_ENV="development"
NEXT_PUBLIC_DEBUG="false"
