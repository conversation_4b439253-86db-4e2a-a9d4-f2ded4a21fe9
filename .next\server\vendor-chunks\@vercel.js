"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics),\n/* harmony export */   track: () => (/* binding */ track)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics,track auto */ // src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isProduction() {\n    return getMode() === \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction removeKey(key, { [key]: _, ...rest }) {\n    return rest;\n}\nfunction parseProperties(properties, options) {\n    if (!properties) return void 0;\n    let props = properties;\n    const errorProperties = [];\n    for (const [key, value] of Object.entries(properties)){\n        if (typeof value === \"object\" && value !== null) {\n            if (options.strip) {\n                props = removeKey(key, props);\n            } else {\n                errorProperties.push(key);\n            }\n        }\n    }\n    if (errorProperties.length > 0 && !options.strip) {\n        throw Error(`The following properties are not valid: ${errorProperties.join(\", \")}. Only strings, numbers, booleans, and null are allowed.`);\n    }\n    return props;\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction track(name2, properties, options) {\n    var _a, _b;\n    if (!isBrowser()) {\n        const msg = \"[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment\";\n        if (isProduction()) {\n            console.warn(msg);\n        } else {\n            throw new Error(msg);\n        }\n        return;\n    }\n    if (!properties) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"event\", {\n            name: name2,\n            options\n        });\n        return;\n    }\n    try {\n        const props = parseProperties(properties, {\n            strip: isProduction()\n        });\n        (_b = window.va) == null ? void 0 : _b.call(window, \"event\", {\n            name: name2,\n            data: props,\n            options\n        });\n    } catch (err) {\n        if (err instanceof Error && isDevelopment()) {\n            console.error(err);\n        }\n    }\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        inject({\n            framework: props.framework || \"react\",\n            basePath: props.basePath ?? getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/react/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/react/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/react/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0),
/* harmony export */   track: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Project\LogoMind\node_modules\@vercel\analytics\dist\react\index.mjs`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Project\LogoMind\node_modules\@vercel\analytics\dist\react\index.mjs#Analytics`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Project\LogoMind\node_modules\@vercel\analytics\dist\react\index.mjs#track`);


/***/ })

};
;