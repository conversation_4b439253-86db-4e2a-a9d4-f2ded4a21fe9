import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { logoGenerator } from '@/lib/ai/generators'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { ratelimit } from '@/lib/ratelimit'

const generateSchema = z.object({
  prompt: z.string().min(1).max(500),
  style: z.string().optional(),
  colors: z.array(z.string()).optional(),
  dimensions: z.string().default('512x512'),
  model: z.enum(['stable-diffusion', 'dall-e', 'custom']).default('stable-diffusion'),
  negativePrompt: z.string().optional(),
  steps: z.number().min(10).max(100).optional(),
  guidance: z.number().min(1).max(20).optional(),
  seed: z.number().optional(),
  projectId: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Rate limiting
    const identifier = session.user.email
    const { success } = await ratelimit.limit(identifier)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please try again later.' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validatedData = generateSchema.parse(body)

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check usage limits
    if (user.generationsUsed >= user.generationsLimit) {
      return NextResponse.json(
        { 
          error: 'Generation limit reached. Please upgrade your plan or wait for next month.',
          code: 'LIMIT_EXCEEDED'
        },
        { status: 403 }
      )
    }

    // Create generation job
    const job = await prisma.generationJob.create({
      data: {
        prompt: validatedData.prompt,
        model: validatedData.model,
        parameters: validatedData,
        userId: user.id,
        projectId: validatedData.projectId,
        status: 'processing',
      },
    })

    try {
      // Generate logo
      const result = await logoGenerator.generateLogo(validatedData)

      // Update job with result
      await prisma.generationJob.update({
        where: { id: job.id },
        data: {
          status: 'completed',
          imageUrl: result.imageUrl,
          completedAt: new Date(),
        },
      })

      // Update user usage
      await prisma.user.update({
        where: { id: user.id },
        data: {
          generationsUsed: {
            increment: 1,
          },
        },
      })

      // If projectId is provided, create logo record
      if (validatedData.projectId) {
        await prisma.logo.create({
          data: {
            name: `Generated Logo - ${new Date().toLocaleDateString()}`,
            prompt: validatedData.prompt,
            imageUrl: result.imageUrl,
            aiModel: validatedData.model,
            style: validatedData.style,
            colors: validatedData.colors || [],
            dimensions: validatedData.dimensions,
            projectId: validatedData.projectId,
          },
        })
      }

      return NextResponse.json({
        success: true,
        data: {
          id: result.id,
          imageUrl: result.imageUrl,
          prompt: result.prompt,
          model: result.model,
          jobId: job.id,
        },
      })

    } catch (generationError) {
      console.error('Generation failed:', generationError)

      // Update job with error
      await prisma.generationJob.update({
        where: { id: job.id },
        data: {
          status: 'failed',
          error: generationError instanceof Error ? generationError.message : 'Unknown error',
          completedAt: new Date(),
        },
      })

      return NextResponse.json(
        { 
          error: 'Logo generation failed. Please try again.',
          jobId: job.id 
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('API error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Invalid request data',
          details: error.errors 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const jobId = searchParams.get('jobId')

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID required' },
        { status: 400 }
      )
    }

    const job = await prisma.generationJob.findUnique({
      where: { id: jobId },
      include: {
        user: {
          select: { email: true },
        },
      },
    })

    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      )
    }

    // Check if user owns this job
    if (job.user.email !== session.user.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: job.id,
        status: job.status,
        prompt: job.prompt,
        model: job.model,
        imageUrl: job.imageUrl,
        error: job.error,
        createdAt: job.createdAt,
        completedAt: job.completedAt,
      },
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
