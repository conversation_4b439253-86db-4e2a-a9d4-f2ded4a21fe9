import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'
import { Analytics } from '@vercel/analytics/react'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: {
    default: 'LogoMind - AI-Powered Brand Identity Platform',
    template: '%s | LogoMind'
  },
  description: 'Create stunning logos and complete brand identities with AI. Professional vector editing, team collaboration, and comprehensive brand asset generation.',
  keywords: [
    'AI logo generator',
    'brand identity',
    'vector graphics',
    'logo design',
    'brand assets',
    'team collaboration',
    'professional design'
  ],
  authors: [{ name: 'HectorTa1989' }],
  creator: 'LogoMind',
  publisher: 'LogoMind',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'LogoMind - AI-Powered Brand Identity Platform',
    description: 'Create stunning logos and complete brand identities with AI. Professional vector editing, team collaboration, and comprehensive brand asset generation.',
    siteName: 'LogoMind',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'LogoMind - AI-Powered Brand Identity Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'LogoMind - AI-Powered Brand Identity Platform',
    description: 'Create stunning logos and complete brand identities with AI. Professional vector editing, team collaboration, and comprehensive brand asset generation.',
    images: ['/og-image.png'],
    creator: '@logomind',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="LogoMind" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
      </head>
      <body className={inter.className}>
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">
              {children}
            </div>
          </div>
          <Toaster />
        </Providers>
        <Analytics />
      </body>
    </html>
  )
}
