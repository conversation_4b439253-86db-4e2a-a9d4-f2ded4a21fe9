'use client'

import { motion } from 'framer-motion'
import { 
  Sparkles, 
  Palette, 
  Users, 
  Download, 
  Zap, 
  Shield,
  Layers,
  Globe
} from 'lucide-react'

const features = [
  {
    icon: Sparkles,
    title: 'AI-Powered Generation',
    description: 'Create professional logos in seconds using advanced AI models like Stable Diffusion and DALL-E.',
  },
  {
    icon: Palette,
    title: 'Professional Vector Editor',
    description: 'Edit and customize your logos with our advanced vector graphics editor built on Fabric.js.',
  },
  {
    icon: Users,
    title: 'Team Collaboration',
    description: 'Work together with your team, share feedback, and manage brand assets in one place.',
  },
  {
    icon: Download,
    title: 'Multiple Export Formats',
    description: 'Download your logos in 20+ formats including SVG, PNG, PDF, and print-ready files.',
  },
  {
    icon: Zap,
    title: 'Lightning Fast',
    description: 'Generate high-quality logos in under 10 seconds with our optimized AI infrastructure.',
  },
  {
    icon: Shield,
    title: 'Commercial License',
    description: 'All generated logos come with full commercial rights and unlimited usage.',
  },
  {
    icon: Layers,
    title: 'Brand Asset Suite',
    description: 'Generate matching business cards, letterheads, and social media assets automatically.',
  },
  {
    icon: Globe,
    title: 'Global CDN',
    description: 'Fast loading times worldwide with our global content delivery network.',
  },
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-20 sm:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
          >
            Everything you need to build your brand
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="mt-4 text-lg text-muted-foreground"
          >
            From AI-powered logo generation to complete brand identity management, 
            LogoMind has all the tools you need.
          </motion.p>
        </div>

        <div className="mx-auto mt-16 max-w-7xl">
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative rounded-2xl border border-border bg-card p-6 shadow-sm transition-shadow hover:shadow-md"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mt-4 text-lg font-semibold text-foreground">
                  {feature.title}
                </h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
