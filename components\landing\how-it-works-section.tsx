'use client'

import { motion } from 'framer-motion'
import { MessageSquare, Sparkles, Download, CheckCircle } from 'lucide-react'

const steps = [
  {
    icon: MessageSquare,
    title: 'Describe Your Vision',
    description: 'Tell us about your brand, style preferences, and what you want your logo to represent.',
  },
  {
    icon: Spark<PERSON>,
    title: 'AI Generates Options',
    description: 'Our AI creates multiple professional logo variations based on your description in seconds.',
  },
  {
    icon: CheckCircle,
    title: 'Customize & Refine',
    description: 'Use our vector editor to perfect your logo, adjust colors, fonts, and layout.',
  },
  {
    icon: Download,
    title: 'Download & Use',
    description: 'Export in any format you need and start using your new brand identity immediately.',
  },
]

export function HowItWorksSection() {
  return (
    <section className="py-20 sm:py-32 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl"
          >
            How it works
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
            className="mt-4 text-lg text-muted-foreground"
          >
            Create professional logos in 4 simple steps
          </motion.p>
        </div>

        <div className="mx-auto mt-16 max-w-4xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {steps.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative text-center"
              >
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-primary text-primary-foreground">
                  <step.icon className="h-8 w-8" />
                </div>
                <h3 className="mt-4 text-lg font-semibold text-foreground">
                  {step.title}
                </h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  {step.description}
                </p>
                {index < steps.length - 1 && (
                  <div className="absolute top-8 left-full hidden w-full lg:block">
                    <div className="h-0.5 w-full bg-border"></div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
