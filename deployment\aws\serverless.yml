service: logomind

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  stage: ${opt:stage, 'dev'}
  environment:
    NODE_ENV: ${self:provider.stage}
    DATABASE_URL: ${env:DATABASE_URL}
    NEXTAUTH_SECRET: ${env:NEXTAUTH_SECRET}
    NEXTAUTH_URL: ${env:NEXTAUTH_URL}
    HUGGINGFACE_API_KEY: ${env:HUGGINGFACE_API_KEY}
    REPLICATE_API_TOKEN: ${env:REPLICATE_API_TOKEN}
    STABILITY_API_KEY: ${env:STABILITY_API_KEY}
    AWS_S3_BUCKET: ${env:AWS_S3_BUCKET}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
          Resource: "arn:aws:s3:::${env:AWS_S3_BUCKET}/*"
        - Effect: Allow
          Action:
            - rds:DescribeDBInstances
            - rds:Connect
          Resource: "*"

plugins:
  - serverless-nextjs-plugin
  - serverless-dotenv-plugin

custom:
  nextjs:
    memory: 1024
    timeout: 30
    runtime: nodejs18.x
    
functions:
  nextjs:
    handler: index.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
      - http:
          path: /
          method: ANY

resources:
  Resources:
    # S3 Bucket for file storage
    LogoMindBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${env:AWS_S3_BUCKET}
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - "*"
              AllowedMethods:
                - GET
                - PUT
                - POST
                - DELETE
              AllowedOrigins:
                - "*"
              MaxAge: 3000
        PublicAccessBlockConfiguration:
          BlockPublicAcls: false
          BlockPublicPolicy: false
          IgnorePublicAcls: false
          RestrictPublicBuckets: false
    
    # CloudFront Distribution
    CloudFrontDistribution:
      Type: AWS::CloudFront::Distribution
      Properties:
        DistributionConfig:
          Origins:
            - DomainName: !GetAtt LogoMindBucket.DomainName
              Id: S3Origin
              S3OriginConfig:
                OriginAccessIdentity: ""
          Enabled: true
          DefaultCacheBehavior:
            TargetOriginId: S3Origin
            ViewerProtocolPolicy: redirect-to-https
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # CachingOptimized
          PriceClass: PriceClass_100
          ViewerCertificate:
            CloudFrontDefaultCertificate: true

  Outputs:
    CloudFrontDistributionId:
      Value: !Ref CloudFrontDistribution
    CloudFrontDomainName:
      Value: !GetAtt CloudFrontDistribution.DomainName
    S3BucketName:
      Value: !Ref LogoMindBucket
