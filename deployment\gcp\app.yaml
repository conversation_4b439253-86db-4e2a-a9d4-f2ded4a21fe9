runtime: nodejs18

env_variables:
  NODE_ENV: production
  DATABASE_URL: ${DATABASE_URL}
  NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
  NEXTAUTH_URL: ${NEXTAUTH_URL}
  HUGGINGFACE_API_KEY: ${HUGGINGFACE_API_KEY}
  REPLICATE_API_TOKEN: ${REPLICATE_API_TOKEN}
  STABILITY_API_KEY: ${STABILITY_API_KEY}
  GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
  GOOGLE_CLIENT_SECRET: ${GOOGLE_CLIENT_SECRET}

automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

handlers:
  - url: /_next/static
    static_dir: .next/static
    secure: always
    
  - url: /.*
    script: auto
    secure: always

skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?tests$
  - ^(.*/)?test$
  - ^Dockerfile$
  - ^README\..*$
  - \.gitignore
  - node_modules/
  - coverage/
