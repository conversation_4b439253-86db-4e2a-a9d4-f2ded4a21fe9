import Replicate from 'replicate'
import { HfInference } from '@huggingface/inference'

// Initialize AI clients
const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
})

const hf = new HfInference(process.env.HUGGINGFACE_API_KEY!)

export interface GenerationParams {
  prompt: string
  style?: string
  colors?: string[]
  dimensions?: string
  model?: 'stable-diffusion' | 'dall-e' | 'custom'
  negativePrompt?: string
  steps?: number
  guidance?: number
  seed?: number
}

export interface GenerationResult {
  id: string
  imageUrl: string
  prompt: string
  model: string
  parameters: GenerationParams
  createdAt: Date
}

export class AILogoGenerator {
  private static instance: AILogoGenerator
  
  public static getInstance(): AILogoGenerator {
    if (!AILogoGenerator.instance) {
      AILogoGenerator.instance = new AILogoGenerator()
    }
    return AILogoGenerator.instance
  }

  async generateLogo(params: GenerationParams): Promise<GenerationResult> {
    const { model = 'stable-diffusion' } = params
    
    try {
      let imageUrl: string
      
      switch (model) {
        case 'stable-diffusion':
          imageUrl = await this.generateWithStableDiffusion(params)
          break
        case 'dall-e':
          imageUrl = await this.generateWithDALLE(params)
          break
        case 'custom':
          imageUrl = await this.generateWithCustomModel(params)
          break
        default:
          throw new Error(`Unsupported model: ${model}`)
      }

      return {
        id: this.generateId(),
        imageUrl,
        prompt: params.prompt,
        model,
        parameters: params,
        createdAt: new Date(),
      }
    } catch (error) {
      console.error('Logo generation failed:', error)
      throw new Error('Failed to generate logo. Please try again.')
    }
  }

  private async generateWithStableDiffusion(params: GenerationParams): Promise<string> {
    const enhancedPrompt = this.enhancePromptForLogo(params.prompt, params.style, params.colors)
    
    const output = await replicate.run(
      "stability-ai/stable-diffusion:ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4",
      {
        input: {
          prompt: enhancedPrompt,
          negative_prompt: params.negativePrompt || "blurry, low quality, watermark, text, signature, copyright, photo, realistic, 3d render",
          width: this.parseDimensions(params.dimensions).width,
          height: this.parseDimensions(params.dimensions).height,
          num_inference_steps: params.steps || 50,
          guidance_scale: params.guidance || 7.5,
          seed: params.seed,
        },
      }
    ) as string[]

    return output[0]
  }

  private async generateWithDALLE(params: GenerationParams): Promise<string> {
    // Note: This would require OpenAI API integration
    // For now, we'll use a placeholder implementation
    const enhancedPrompt = this.enhancePromptForLogo(params.prompt, params.style, params.colors)
    
    // Placeholder - replace with actual OpenAI API call
    throw new Error('DALL-E integration not implemented yet')
  }

  private async generateWithCustomModel(params: GenerationParams): Promise<string> {
    const enhancedPrompt = this.enhancePromptForLogo(params.prompt, params.style, params.colors)
    
    try {
      const response = await hf.textToImage({
        model: 'runwayml/stable-diffusion-v1-5',
        inputs: enhancedPrompt,
        parameters: {
          negative_prompt: params.negativePrompt || "blurry, low quality, watermark, text",
          num_inference_steps: params.steps || 30,
          guidance_scale: params.guidance || 7.5,
          width: this.parseDimensions(params.dimensions).width,
          height: this.parseDimensions(params.dimensions).height,
        },
      })

      // Convert blob to URL
      const blob = response as Blob
      return URL.createObjectURL(blob)
    } catch (error) {
      console.error('Hugging Face generation failed:', error)
      throw error
    }
  }

  private enhancePromptForLogo(
    prompt: string, 
    style?: string, 
    colors?: string[]
  ): string {
    let enhancedPrompt = `professional logo design, ${prompt}`
    
    // Add style modifiers
    if (style) {
      const styleModifiers = {
        minimalist: 'clean, simple, minimalist design',
        modern: 'modern, contemporary, sleek',
        vintage: 'vintage, retro, classic',
        playful: 'playful, fun, creative',
        elegant: 'elegant, sophisticated, refined',
        bold: 'bold, strong, impactful',
        geometric: 'geometric shapes, abstract',
        organic: 'organic shapes, natural forms',
      }
      
      enhancedPrompt += `, ${styleModifiers[style as keyof typeof styleModifiers] || style}`
    }
    
    // Add color information
    if (colors && colors.length > 0) {
      enhancedPrompt += `, using colors: ${colors.join(', ')}`
    }
    
    // Add logo-specific terms
    enhancedPrompt += ', vector graphics, scalable, brand identity, corporate logo, high contrast, clear shapes'
    
    return enhancedPrompt
  }

  private parseDimensions(dimensions: string = '512x512'): { width: number; height: number } {
    const [width, height] = dimensions.split('x').map(Number)
    return { width: width || 512, height: height || 512 }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  async generateVariations(
    originalParams: GenerationParams, 
    count: number = 4
  ): Promise<GenerationResult[]> {
    const variations: GenerationResult[] = []
    
    for (let i = 0; i < count; i++) {
      const variationParams = {
        ...originalParams,
        seed: Math.floor(Math.random() * 1000000),
        // Slightly modify the prompt for variation
        prompt: this.createPromptVariation(originalParams.prompt, i),
      }
      
      try {
        const result = await this.generateLogo(variationParams)
        variations.push(result)
      } catch (error) {
        console.error(`Failed to generate variation ${i + 1}:`, error)
        // Continue with other variations
      }
    }
    
    return variations
  }

  private createPromptVariation(originalPrompt: string, index: number): string {
    const variations = [
      originalPrompt,
      `${originalPrompt}, alternative design`,
      `${originalPrompt}, different style`,
      `${originalPrompt}, creative interpretation`,
    ]
    
    return variations[index] || originalPrompt
  }

  async enhanceImage(imageUrl: string, enhancement: 'upscale' | 'vectorize'): Promise<string> {
    try {
      if (enhancement === 'upscale') {
        const output = await replicate.run(
          "nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc3a73abf41610695738c1d7b",
          {
            input: {
              image: imageUrl,
              scale: 4,
            },
          }
        ) as string

        return output
      } else if (enhancement === 'vectorize') {
        // Placeholder for vectorization - would need specialized service
        throw new Error('Vectorization not implemented yet')
      }
      
      throw new Error('Unknown enhancement type')
    } catch (error) {
      console.error('Image enhancement failed:', error)
      throw error
    }
  }
}

export const logoGenerator = AILogoGenerator.getInstance()
