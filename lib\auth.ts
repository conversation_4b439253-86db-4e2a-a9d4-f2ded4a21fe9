import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import GoogleProvider from 'next-auth/providers/google'
import GitHubProvider from 'next-auth/providers/github'
import EmailProvider from 'next-auth/providers/email'
import { prisma } from '@/lib/prisma'
import { Adapter } from 'next-auth/adapters'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma) as Adapter,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: process.env.EMAIL_SERVER_PORT,
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Persist the OAuth access_token and or the user id to the token right after signin
      if (account) {
        token.accessToken = account.access_token
      }
      
      if (user) {
        token.id = user.id
        
        // Get user data from database
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email! },
          select: {
            id: true,
            subscriptionTier: true,
            subscriptionStatus: true,
            generationsUsed: true,
            generationsLimit: true,
          },
        })
        
        if (dbUser) {
          token.subscriptionTier = dbUser.subscriptionTier
          token.subscriptionStatus = dbUser.subscriptionStatus
          token.generationsUsed = dbUser.generationsUsed
          token.generationsLimit = dbUser.generationsLimit
        }
      }
      
      return token
    },
    async session({ session, token }) {
      // Send properties to the client
      if (token) {
        session.user.id = token.id as string
        session.user.subscriptionTier = token.subscriptionTier as string
        session.user.subscriptionStatus = token.subscriptionStatus as string
        session.user.generationsUsed = token.generationsUsed as number
        session.user.generationsLimit = token.generationsLimit as number
      }
      
      return session
    },
    async signIn({ user, account, profile }) {
      // Allow sign in
      return true
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    },
  },
  events: {
    async createUser({ user }) {
      // Set default subscription tier and limits for new users
      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscriptionTier: 'free',
          subscriptionStatus: 'active',
          generationsLimit: 10, // Free tier limit
        },
      })
    },
    async signIn({ user, account, profile, isNewUser }) {
      console.log('User signed in:', { 
        userId: user.id, 
        email: user.email, 
        isNewUser 
      })
    },
    async signOut({ session, token }) {
      console.log('User signed out:', { userId: token?.id })
    },
  },
  debug: process.env.NODE_ENV === 'development',
}

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name?: string | null
      image?: string | null
      subscriptionTier: string
      subscriptionStatus: string
      generationsUsed: number
      generationsLimit: number
    }
  }

  interface User {
    subscriptionTier?: string
    subscriptionStatus?: string
    generationsUsed?: number
    generationsLimit?: number
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    subscriptionTier: string
    subscriptionStatus: string
    generationsUsed: number
    generationsLimit: number
  }
}
