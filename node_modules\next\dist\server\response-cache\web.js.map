{"version": 3, "sources": ["../../../src/server/response-cache/web.ts"], "names": ["WebResponseCache", "constructor", "minimalMode", "pendingResponses", "Map", "Object", "assign", "get", "key", "responseGenerator", "context", "pendingResponseKey", "isOnDemandRevalidate", "pendingResponse", "promise", "resolve", "resolver", "reject", "rejecter", "Detached<PERSON>romise", "set", "resolved", "cacheEntry", "Promise", "previousCacheItem", "expiresAt", "Date", "now", "entry", "delete", "resolveValue", "isMiss", "revalidate", "undefined", "err", "console", "error"], "mappings": ";;;;+BAGA;;;CAGC,GACD;;;eAAqBA;;;iCAPW;AAOjB,MAAMA;IASnBC,YAAYC,WAAoB,CAAE;QAChC,IAAI,CAACC,gBAAgB,GAAG,IAAIC;QAC5B,4EAA4E;QAC5E,qEAAqE;QACrEC,OAAOC,MAAM,CAAC,IAAI,EAAE;YAAEJ;QAAY;IACpC;IAEOK,IACLC,GAAkB,EAClBC,iBAAoC,EACpCC,OAIC,EACmC;YA0ClC;QAzCF,4DAA4D;QAC5D,MAAMC,qBAAqBH,MACvB,CAAC,EAAEA,IAAI,CAAC,EAAEE,QAAQE,oBAAoB,GAAG,MAAM,IAAI,CAAC,GACpD;QAEJ,MAAMC,kBAAkBF,qBACpB,IAAI,CAACR,gBAAgB,CAACI,GAAG,CAACI,sBAC1B;QACJ,IAAIE,iBAAiB;YACnB,OAAOA;QACT;QAEA,MAAM,EACJC,OAAO,EACPC,SAASC,QAAQ,EACjBC,QAAQC,QAAQ,EACjB,GAAG,IAAIC,gCAAe;QACvB,IAAIR,oBAAoB;YACtB,IAAI,CAACR,gBAAgB,CAACiB,GAAG,CAACT,oBAAoBG;QAChD;QAEA,IAAIO,WAAW;QACf,MAAMN,UAAU,CAACO;YACf,IAAIX,oBAAoB;gBACtB,wDAAwD;gBACxD,IAAI,CAACR,gBAAgB,CAACiB,GAAG,CACvBT,oBACAY,QAAQR,OAAO,CAACO;YAEpB;YACA,IAAI,CAACD,UAAU;gBACbA,WAAW;gBACXL,SAASM;YACX;QACF;QAEA,sDAAsD;QACtD,yDAAyD;QACzD,IACEX,sBACA,IAAI,CAACT,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACsB,iBAAiB,qBAAtB,wBAAwBhB,GAAG,MAAKG,sBAChC,IAAI,CAACa,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;YACAZ,QAAQ,IAAI,CAACS,iBAAiB,CAACI,KAAK;YACpC,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,CAAClB;YAC7B,OAAOG;QACT;QAKE,CAAA;YACA,IAAI;gBACF,MAAMQ,aAAa,MAAMb,kBAAkBY;gBAC3C,MAAMS,eACJR,eAAe,OACX,OACA;oBACE,GAAGA,UAAU;oBACbS,QAAQ;gBACV;gBAEN,8DAA8D;gBAC9D,IAAI,CAACrB,QAAQE,oBAAoB,EAAE;oBACjCG,QAAQe;gBACV;gBAEA,IAAItB,OAAOc,cAAc,OAAOA,WAAWU,UAAU,KAAK,aAAa;oBACrE,IAAI,CAACR,iBAAiB,GAAG;wBACvBhB,KAAKG,sBAAsBH;wBAC3BoB,OAAON;wBACPG,WAAWC,KAAKC,GAAG,KAAK;oBAC1B;gBACF,OAAO;oBACL,IAAI,CAACH,iBAAiB,GAAGS;gBAC3B;gBAEA,IAAIvB,QAAQE,oBAAoB,EAAE;oBAChCG,QAAQe;gBACV;YACF,EAAE,OAAOI,KAAK;gBACZ,0DAA0D;gBAC1D,4DAA4D;gBAC5D,IAAIb,UAAU;oBACZc,QAAQC,KAAK,CAACF;gBAChB,OAAO;oBACLhB,SAASgB;gBACX;YACF,SAAU;gBACR,IAAIvB,oBAAoB;oBACtB,IAAI,CAACR,gBAAgB,CAAC0B,MAAM,CAAClB;gBAC/B;YACF;QACF,CAAA;QACA,OAAOG;IACT;AACF"}