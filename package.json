{"name": "logomind", "version": "1.0.0", "description": "AI-Powered Brand Identity Platform", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth/prisma-adapter": "^1.0.12", "@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.7.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-button": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.38.5", "@types/fabric": "^5.3.7", "@vercel/analytics": "^1.1.1", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "fabric": "^5.3.0", "framer-motion": "^10.16.16", "huggingface": "^1.4.0", "lucide-react": "^0.294.0", "next": "14.0.4", "next-auth": "^4.24.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "replicate": "^0.25.1", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "prettier": "^3.1.0", "prisma": "^5.7.1", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}, "keywords": ["ai", "logo", "brand", "design", "vector", "graphics", "nextjs", "typescript"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/logomind.git"}, "bugs": {"url": "https://github.com/HectorTa1989/logomind/issues"}, "homepage": "https://github.com/HectorTa1989/logomind#readme"}