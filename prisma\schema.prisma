// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Subscription info
  subscriptionTier String @default("free") // free, starter, professional, team, enterprise
  subscriptionStatus String @default("active") // active, cancelled, expired
  subscriptionEndsAt DateTime?
  
  // Usage tracking
  generationsUsed Int @default(0)
  generationsLimit Int @default(10) // Monthly limit based on tier
  
  accounts Account[]
  sessions Session[]
  projects Project[]
  teamMemberships TeamMembership[]
  ownedTeams Team[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Team {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  ownerId     String
  owner       User @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  
  members     TeamMembership[]
  projects    Project[]
}

model TeamMembership {
  id     String @id @default(cuid())
  role   String @default("member") // owner, admin, member, viewer
  
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  teamId String
  team   Team   @relation(fields: [teamId], references: [id], onDelete: Cascade)
  
  createdAt DateTime @default(now())
  
  @@unique([userId, teamId])
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Project settings
  isPublic    Boolean @default(false)
  tags        String[] // Array of tags for organization
  
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  teamId      String?
  team        Team?    @relation(fields: [teamId], references: [id], onDelete: SetNull)
  
  logos       Logo[]
  brandAssets BrandAsset[]
}

model Logo {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Logo data
  prompt      String   @db.Text
  imageUrl    String
  svgData     String?  @db.Text
  vectorData  String?  @db.Text // Fabric.js canvas data
  
  // Generation metadata
  aiModel     String   // stable-diffusion, dall-e, custom
  style       String?
  colors      String[] // Array of hex colors
  dimensions  String   @default("512x512")
  
  // Status and versioning
  status      String   @default("draft") // draft, final, archived
  version     Int      @default(1)
  isPublic    Boolean  @default(false)
  
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  
  variations  LogoVariation[]
  exports     LogoExport[]
}

model LogoVariation {
  id          String   @id @default(cuid())
  name        String?
  imageUrl    String
  svgData     String?  @db.Text
  vectorData  String?  @db.Text
  prompt      String   @db.Text
  createdAt   DateTime @default(now())
  
  logoId      String
  logo        Logo     @relation(fields: [logoId], references: [id], onDelete: Cascade)
}

model LogoExport {
  id          String   @id @default(cuid())
  format      String   // png, jpg, svg, pdf, eps, etc.
  size        String   // 512x512, 1024x1024, etc.
  fileUrl     String
  fileSize    Int      // in bytes
  createdAt   DateTime @default(now())
  
  logoId      String
  logo        Logo     @relation(fields: [logoId], references: [id], onDelete: Cascade)
}

model BrandAsset {
  id          String   @id @default(cuid())
  name        String
  type        String   // business-card, letterhead, social-media, etc.
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Asset data
  imageUrl    String
  vectorData  String?  @db.Text
  templateId  String?
  
  // Dimensions and format
  dimensions  String
  format      String   @default("png")
  
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model Template {
  id          String   @id @default(cuid())
  name        String
  description String?
  category    String   // business-card, letterhead, social-media, etc.
  tags        String[]
  
  // Template data
  previewUrl  String
  templateData String @db.Text // JSON template structure
  
  // Metadata
  isPublic    Boolean  @default(true)
  isPremium   Boolean  @default(false)
  downloads   Int      @default(0)
  rating      Float    @default(0)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model GenerationJob {
  id          String   @id @default(cuid())
  status      String   @default("pending") // pending, processing, completed, failed
  prompt      String   @db.Text
  model       String
  parameters  Json?    // Model-specific parameters
  
  // Results
  imageUrl    String?
  error       String?
  
  // Metadata
  userId      String
  projectId   String?
  createdAt   DateTime @default(now())
  completedAt DateTime?
  
  @@index([userId, status])
  @@index([createdAt])
}
