{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "rewrites": [{"source": "/api/ai/(.*)", "destination": "/api/ai/$1"}], "env": {"DATABASE_URL": "@database_url", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url", "HUGGINGFACE_API_KEY": "@huggingface_api_key", "REPLICATE_API_TOKEN": "@replicate_api_token", "STABILITY_API_KEY": "@stability_api_key", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "GITHUB_CLIENT_ID": "@github_client_id", "GITHUB_CLIENT_SECRET": "@github_client_secret"}}